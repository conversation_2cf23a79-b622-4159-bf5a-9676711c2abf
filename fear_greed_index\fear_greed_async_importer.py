# -*- coding: utf-8 -*-
import asyncio
import logging
import traceback
import json
import os
from pathlib import Path
import time
from datetime import date, datetime, timedelta
from typing import Optional, Dict, Any, List, Tuple
import configparser
import requests # Added import

# import aiohttp # Removed import
import asyncpg
import pandas as pd # 可选，但用于数据处理可能方便
# import schedule # 可选的调度库

# --- 配置 (稍后从外部加载) ---
# 建议使用 configparser 或环境变量替换硬编码
CONFIG = {
    'database': {
        'dbname': 'ts_research',
        'user': 'postgres',
        'password': 'xh123', # 警告：生产环境切勿硬编码密码
        'host': 'localhost',
        'port': 5432
    },
    'api': {
        'url': "https://production.dataviz.cnn.io/index/fearandgreed/graphdata",
        'headers': {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        },
        'timeout': 30 # 请求超时时间（秒）
    },
    'logging': {
        'log_file': './fear_greed_importer.log',
        'log_level': 'INFO' # 可以是 DEBUG, INFO, WARNING, ERROR, CRITICAL
    },
    'import': {
        'batch_size': 500 # 数据库批量导入大小
    }
}

# --- 配置加载 ---
def load_config(config_file='config.ini'):
    """从 .ini 文件加载配置，如果文件或键不存在则使用默认值"""
    parser = configparser.ConfigParser()
    # 使用 Path 对象处理路径
    config_path = Path(config_file)

    if not config_path.is_file():
        logging.warning(f"配置文件 '{config_file}' 未找到，将使用内置的默认配置。")
        return CONFIG # 返回原始的默认 CONFIG

    try:
        parser.read(config_path, encoding='utf-8')
        logging.info(f"从 '{config_file}' 加载配置。")

        # 覆盖默认值 - 显式读取每个部分和键，提供回退
        db_conf = CONFIG['database']
        db_conf['dbname'] = parser.get('database', 'dbname', fallback=db_conf['dbname'])
        db_conf['user'] = parser.get('database', 'user', fallback=db_conf['user'])
        db_conf['password'] = parser.get('database', 'password', fallback=db_conf['password'])
        db_conf['host'] = parser.get('database', 'host', fallback=db_conf['host'])
        # 注意：port 需要是整数
        db_conf['port'] = parser.getint('database', 'port', fallback=db_conf['port'])

        api_conf = CONFIG['api']
        api_conf['url'] = parser.get('api', 'url', fallback=api_conf['url'])
        # headers 比较复杂，通常不在 ini 中配置，除非特殊需要
        api_conf['timeout'] = parser.getint('api', 'timeout', fallback=api_conf['timeout'])

        log_conf = CONFIG['logging']
        log_conf['log_file'] = parser.get('logging', 'log_file', fallback=log_conf['log_file'])
        log_conf['log_level'] = parser.get('logging', 'log_level', fallback=log_conf['log_level'])

        import_conf = CONFIG['import']
        import_conf['batch_size'] = parser.getint('import', 'batch_size', fallback=import_conf['batch_size'])

    except configparser.Error as e:
        logging.error(f"读取配置文件 '{config_file}' 时出错: {e}。将使用内置默认配置。")
        return CONFIG # 出错时也返回默认配置
    except ValueError as e:
         logging.error(f"配置文件 '{config_file}' 中数值转换错误 (例如 port, timeout, batch_size): {e}。将使用内置默认配置。")
         return CONFIG # 出错时也返回默认配置

    return CONFIG # 返回更新后的 CONFIG

# 重新定义 LOG_FILE，因为配置可能已更改
LOG_FILE = Path(CONFIG['logging']['log_file'])


# --- 日志设置 ---
def setup_logging(log_file, log_level_str='INFO'):
    """配置日志记录，同时输出到文件和控制台"""
    log_level_map = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL
    }
    log_level = log_level_map.get(log_level_str.upper(), logging.INFO)

    log_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')

    # 文件处理器
    try:
        log_file.parent.mkdir(parents=True, exist_ok=True) # 确保目录存在
        file_handler = logging.FileHandler(log_file, encoding='utf-8', mode='a') # 使用追加模式
        file_handler.setFormatter(log_formatter)
        file_handler.setLevel(log_level)
    except Exception as e:
        print(f"错误：无法配置日志文件处理器: {e}")
        file_handler = None

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(log_formatter)
    console_handler.setLevel(log_level)

    # 获取根 logger
    logger = logging.getLogger()
    logger.setLevel(log_level)

    # 清除可能存在的旧处理器，确保配置干净
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
        handler.close() # 关闭处理器

    # 添加新的处理器
    if file_handler:
        logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    logging.info(f"日志系统已配置。级别: {log_level_str}, 文件: {log_file}")

# --- 数据库辅助函数 (异步) ---
async def create_db_pool() -> Optional[asyncpg.Pool]:
    """创建 asyncpg 连接池"""
    db_conf = CONFIG['database']
    try:
        pool = await asyncpg.create_pool(
            database=db_conf['dbname'],
            user=db_conf['user'],
            password=db_conf['password'],
            host=db_conf['host'],
            port=db_conf['port'],
            min_size=1, # 保持至少一个连接
            max_size=10 # 最大连接数
        )
        logging.info(f"成功创建连接到 '{db_conf['host']}:{db_conf['port']}/{db_conf['dbname']}' 的数据库连接池。")
        return pool
    except asyncpg.exceptions.InvalidPasswordError:
        logging.error("数据库连接失败：密码错误。")
        return None
    except asyncpg.exceptions.CannotConnectNowError as e:
         logging.error(f"数据库连接失败：无法立即连接。服务器可能过载或正在启动。错误: {e}")
         return None
    except ConnectionRefusedError as e:
        logging.error(f"数据库连接失败：连接被拒绝。请检查主机 '{db_conf['host']}:{db_conf['port']}' 是否可达且 PostgreSQL 服务正在运行。错误: {e}")
        return None
    except Exception as e:
        logging.error(f"创建数据库连接池时发生未知错误: {e}", exc_info=True)
        return None

async def ensure_fear_greed_table_exists(pool: asyncpg.Pool):
    """确保 fear_greed_index_daily 表存在"""
    CREATE_TABLE_SQL = """
    CREATE TABLE IF NOT EXISTS public.fear_greed_index_daily (
        date DATE PRIMARY KEY,                     -- 数据日期 (主键)
        fg_value SMALLINT NOT NULL,                -- 指数值 (0-100)
        rating VARCHAR(20) NOT NULL,               -- 市场评级
        update_time TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP -- 更新时间戳
    );
    """
    CREATE_INDEX_SQL = """
    CREATE INDEX IF NOT EXISTS idx_fg_update_time ON public.fear_greed_index_daily (update_time);
    """
    async with pool.acquire() as conn:
        async with conn.transaction(): # DDL 语句也最好在事务中执行
            try:
                logging.info("检查并确保 public.fear_greed_index_daily 表存在...")
                await conn.execute(CREATE_TABLE_SQL)
                logging.info("执行 CREATE TABLE IF NOT EXISTS 语句完成。")
                await conn.execute(CREATE_INDEX_SQL)
                logging.info("执行 CREATE INDEX IF NOT EXISTS 语句完成。")
                logging.info("数据库表结构检查/创建完成。")
                return True
            except Exception as e:
                logging.error(f"创建或检查数据库表 'public.fear_greed_index_daily' 时出错: {e}", exc_info=True)
                # 事务会自动回滚
                return False

async def get_last_fear_greed_date(pool: asyncpg.Pool) -> Optional[date]:
    """获取数据库中最新的恐惧贪婪指数日期"""
    GET_MAX_DATE_SQL = "SELECT MAX(date) FROM public.fear_greed_index_daily;"
    async with pool.acquire() as conn:
        try:
            max_date_record = await conn.fetchrow(GET_MAX_DATE_SQL)
            if max_date_record and max_date_record[0]:
                last_date = max_date_record[0]
                logging.info(f"数据库中最新的恐惧贪婪指数日期为: {last_date}")
                return last_date
            else:
                logging.info("数据库中尚无恐惧贪婪指数数据，将获取所有历史数据。")
                return None
        except asyncpg.exceptions.UndefinedTableError:
             logging.warning("表 'public.fear_greed_index_daily' 不存在，无法获取最新日期。将获取所有历史数据。")
             return None
        except Exception as e:
            logging.error(f"查询最新日期时发生错误: {e}", exc_info=True)
            return None # 出错时返回 None，后续逻辑应按无最新日期处理

async def batch_import_fear_greed_data(pool: asyncpg.Pool, data_tuples: List[Tuple[date, int, str]], batch_size: int):
    """批量导入数据到数据库 (Upsert)，借鉴 VIX 爬虫的批处理和错误处理逻辑"""
    if not data_tuples:
        logging.info("没有数据需要导入，跳过批量导入。")
        return 0, 0

    # SQL for INSERT ON CONFLICT (Upsert)
    # PRIMARY KEY is 'date'
    insert_sql = """
    INSERT INTO public.fear_greed_index_daily (date, fg_value, rating)
    VALUES ($1, $2, $3)
    ON CONFLICT (date) DO UPDATE SET
      fg_value = EXCLUDED.fg_value,
      rating = EXCLUDED.rating,
      update_time = CURRENT_TIMESTAMP;
    """
    total_imported = 0
    total_errors = 0

    for i in range(0, len(data_tuples), batch_size):
        batch = data_tuples[i:i + batch_size]
        if not batch:
            continue

        conn = None # Define conn outside try/finally for proper release
        try:
            conn = await pool.acquire() # Acquire connection for the batch
            # Use transaction context manager for automatic commit/rollback
            async with conn.transaction():
                # Execute the batch insert/update
                await conn.executemany(insert_sql, batch)
                # If no exception, the batch was successful
                batch_imported_count = len(batch)
                total_imported += batch_imported_count
                logging.debug(f"成功处理批次 (大小: {batch_imported_count}, 起始索引: {i})。")
            # Transaction commits automatically if no error occurred

        except asyncpg.PostgresError as e: # Catch specific database errors
            # Transaction rolls back automatically due to exception
            batch_error_count = len(batch)
            total_errors += batch_error_count
            logging.error(f"数据库错误导致批次处理失败 (大小: {batch_error_count}, 起始索引: {i}): {e} [SQLSTATE: {e.sqlstate}]")
            logging.warning(f"批次 (起始索引: {i}) 的事务已回滚，将继续处理下一个批次。")
            # Continue to the next iteration of the loop

        except Exception as e: # Catch other unexpected errors during batch processing
            # Transaction likely rolls back automatically
            batch_error_count = len(batch)
            total_errors += batch_error_count
            logging.critical(f"处理批次 (起始索引: {i}) 时发生意外错误: {e}", exc_info=True)
            logging.warning(f"批次 (起始索引: {i}) 的事务可能已回滚，将继续处理下一个批次。")
            # Continue to the next iteration of the loop

        finally:
            if conn:
                await pool.release(conn) # Ensure connection is always released

    logging.info(f"批量导入处理完成。总共成功 Upsert {total_imported} 行，遇到 {total_errors} 行错误（跨越多个失败批次）。")
    return total_imported, total_errors

# --- 数据获取函数 (同步) --- NEW
def fetch_fear_greed_data_sync(url: str, headers: dict, timeout: int) -> Optional[dict]:
    """同步获取恐惧贪婪指数 API 数据"""
    logging.info(f"开始同步获取 API 数据: {url}")
    try:
        response = requests.get(url, headers=headers, timeout=timeout)
        response.raise_for_status()  # 检查 HTTP 错误状态码 (4xx or 5xx)

        # 检查响应类型，确保是 JSON
        content_type = response.headers.get('Content-Type', '')
        if 'application/json' in content_type:
            data = response.json()
            logging.info(f"API 数据获取成功 (状态码: {response.status_code})。")
            return data
        else:
            logging.error(f"API 响应不是预期的 JSON 格式 (Content-Type: {content_type})。URL: {url}")
            logging.debug(f"响应内容预览: {response.text[:500]}...") # 记录前500字符
            return None

    except requests.exceptions.Timeout:
        logging.error(f"请求超时 ({timeout}秒): {url}")
        return None
    except requests.exceptions.HTTPError as e:
        # raise_for_status 会触发这个异常
        logging.error(f"HTTP 错误: {e.response.status_code} {e.response.reason} - URL: {url}")
        # 特别处理 418 错误，虽然我们期望 requests 能绕过它
        if e.response.status_code == 418:
             logging.warning("服务器返回了 HTTP 418 'I'm a teapot' 错误。这通常是反爬虫机制。")
        return None
    except requests.exceptions.ConnectionError as e:
        logging.error(f"连接错误: {e} - URL: {url}")
        return None
    except requests.exceptions.RequestException as e: # 捕获 requests 的其他异常
        logging.error(f"请求过程中发生 requests 错误: {e} - URL: {url}")
        logging.debug(traceback.format_exc())
        return None
    except json.JSONDecodeError as e: # requests >= 2.27 会在 response.json() 中抛出这个
        logging.error(f"解析 JSON 响应时出错: {e} - URL: {url}")
        logging.debug(f"原始响应内容 (可能部分): {response.text[:500]}...")
        return None
    except Exception as e: # Catch any other unexpected errors
        logging.error(f"获取 API 数据时发生未知错误: {e}", exc_info=True)
        logging.debug(traceback.format_exc())
        return None

# --- 数据解析函数 ---
def parse_fear_greed_response(response_data: dict) -> List[Dict[str, Any]]:
    """解析 API 响应，返回包含 date, fg_value, rating 的字典列表"""
    parsed_records = []
    if not response_data or 'fear_and_greed_historical' not in response_data or 'data' not in response_data['fear_and_greed_historical']:
        logging.error("API 响应数据格式无效或缺少 'fear_and_greed_historical' 或 'data' 键。")
        return parsed_records # 返回空列表

    historical_data = response_data['fear_and_greed_historical']['data']
    if not isinstance(historical_data, list):
        logging.error("API 响应中的 'data' 部分不是列表。")
        return parsed_records # 返回空列表

    for item in historical_data:
        if not isinstance(item, dict) or 'x' not in item or 'y' not in item or 'rating' not in item:
            logging.warning(f"跳过格式不正确的历史数据项: {item}")
            continue

        try:
            # CNN 使用毫秒级 Unix 时间戳
            timestamp_ms = float(item['x'])
            # 转换为 datetime 对象，然后取 date 部分
            date_obj = datetime.fromtimestamp(timestamp_ms / 1000).date()

            # 确保 fg_value 是整数
            fg_value = int(round(float(item['y']))) # 四舍五入到最近的整数

            rating = str(item['rating']).strip()

            parsed_records.append({
                'date': date_obj,
                'fg_value': fg_value,
                'rating': rating
            })

        except (ValueError, TypeError) as e:
            logging.warning(f"解析或转换数据项时出错: {item} - 错误: {e}")
            continue

    logging.info(f"成功解析 {len(parsed_records)} 条恐惧贪婪指数记录。")
    # 按日期升序排序（API 可能不保证顺序）
    parsed_records.sort(key=lambda x: x['date'])
    return parsed_records

# --- 主任务函数 (异步) ---
async def run_daily_fg_job_and_import():
    """执行单次的恐惧贪婪指数数据获取、解析和数据库导入任务"""
    logging.info("====== 开始执行每日恐惧贪婪指数数据获取与导入任务 ======")
    start_time = time.time()
    overall_success = False
    imported_count = 0  # 初始化导入计数
    error_count = 0     # 初始化错误计数

    pool = None
    # session = None # Removed session variable

    try:
        # 1. 创建数据库连接池
        pool = await create_db_pool()
        if not pool:
            # create_db_pool 内部会记录错误
            raise Exception("无法创建数据库连接池")

        # Removed aiohttp session creation
        logging.info("asyncpg 连接池已创建。")


        # 2. 确保表存在 (获取数据前先确保表存在，以便查询最新日期)
        table_ready = await ensure_fear_greed_table_exists(pool)
        if not table_ready:
            raise Exception("无法确保数据库表存在，任务中止。")

        # 3. 获取 API 数据 (同步调用)
        raw_data = fetch_fear_greed_data_sync(
            CONFIG['api']['url'],
            CONFIG['api']['headers'],
            CONFIG['api']['timeout']
        )

        if raw_data is None: # fetch_fear_greed_data_sync 在失败时返回 None
            # fetch_fear_greed_data_sync 内部会记录错误
            raise Exception("未能获取 API 数据，任务中止。")

        # 4. 解析数据
        parsed_data = parse_fear_greed_response(raw_data)
        if not parsed_data:
            logging.warning("解析 API 响应后未获得有效数据记录。")
            # 即使没有解析到数据，也视为任务成功（可能 API 今天没数据）
            overall_success = True
            # 直接跳到 finally 进行清理
            # 不再使用 return，让 finally 块执行
        else:
            # 只有在解析到数据时才继续后续步骤
            # 5. 获取数据库中的最新日期
            last_date_in_db = await get_last_fear_greed_date(pool)

            # 6. 过滤新数据
            if last_date_in_db:
                # 确保比较的是 date 对象
                new_data_to_process = [item for item in parsed_data if isinstance(item.get('date'), date) and item['date'] > last_date_in_db]
                logging.info(f"根据数据库最新日期 {last_date_in_db}，过滤得到 {len(new_data_to_process)} 条新记录。")
            else:
                # 如果数据库为空或查询失败，则处理所有解析到的有效数据
                new_data_to_process = [item for item in parsed_data if isinstance(item.get('date'), date)]
                logging.info(f"数据库为空或无法获取最新日期，将处理所有 {len(new_data_to_process)} 条已解析的有效记录。")

            # 7. 准备元组并导入
            if new_data_to_process:
                data_tuples = [
                    (item['date'], item['fg_value'], item['rating'])
                    for item in new_data_to_process
                    # 再次确保数据有效性，虽然 parse 函数已处理，但多一层保险
                    if isinstance(item.get('date'), date) and isinstance(item.get('fg_value'), int) and isinstance(item.get('rating'), str)
                ]
                if data_tuples: # 可能过滤后为空
                    logging.info(f"准备将 {len(data_tuples)} 条新记录导入数据库...")
                    imported_count, error_count = await batch_import_fear_greed_data(
                        pool,
                        data_tuples,
                        CONFIG['import']['batch_size']
                    )
                    # 只要尝试了导入（无论是否有错误），就认为主要流程成功
                    # 具体的成功/失败细节由 batch_import 函数记录
                    overall_success = True # 标记为成功，错误计数已记录
                else:
                    logging.info("过滤或准备元组后没有数据需要导入。")
                    overall_success = True # 没有数据导入也是成功
            else:
                logging.info("没有新的数据需要导入数据库。")
                overall_success = True # 没有新数据也是一种成功状态

    except Exception as e:
        logging.critical(f"任务执行过程中发生严重错误: {e}", exc_info=True)
        overall_success = False # 明确标记为失败
    finally:
        # 8. 清理资源
        # Removed session closing logic
        if pool:
            # For asyncpg versions < 0.29.0 use pool.close(), for >= 0.29.0 use pool.aclose()
            # Using pool.close() for broader compatibility for now. If 0.29+ is guaranteed, use await pool.aclose()
            await pool.close()
            logging.info("asyncpg 连接池已关闭。") # Updated log message

        end_time = time.time()
        duration = end_time - start_time
        logging.info(f"====== 每日恐惧贪婪指数数据获取与导入任务结束 (耗时: {duration:.2f} 秒) ======")
        status_msg = f"最终状态: {'成功' if overall_success else '失败'}"
        # 报告导入计数和错误计数，即使 overall_success 为 False，也可能部分导入
        status_msg += f" (成功导入/更新: {imported_count}, 导入时错误行数: {error_count})"
        logging.info(status_msg)


# --- 入口点 ---
if __name__ == "__main__":
    # 1. 加载配置 (在设置日志之前，这样日志配置也能被覆盖)
    # 注意：此时日志可能尚未完全配置，错误可能只打印到控制台
    try:
        # 尝试加载配置，如果失败，CONFIG 仍包含默认值
        CONFIG = load_config('config.ini')
        # 确保 LOG_FILE 路径使用加载后的配置
        LOG_FILE = Path(CONFIG['logging']['log_file'])
    except Exception as config_e:
        # 捕获加载配置时的意外错误
        print(f"CRITICAL: 加载配置时发生严重错误: {config_e}")
        # 使用默认 LOG_FILE 尝试设置日志
        LOG_FILE = Path(CONFIG['logging']['log_file']) # 确保 LOG_FILE 被定义

    # 2. 设置日志 (使用可能已更新的配置)
    setup_logging(LOG_FILE, CONFIG['logging']['log_level'])
    logging.info("脚本启动...")
    logging.debug(f"使用的配置: {CONFIG}") # 记录使用的最终配置

    # 3. 立即运行一次 ---
    try:
        # Python 3.7+
        asyncio.run(run_daily_fg_job_and_import())
    except KeyboardInterrupt:
        logging.info("脚本被手动中断。")
    except Exception as e:
        logging.critical(f"脚本顶层运行出错: {e}", exc_info=True)

    # --- 可选：调度运行 ---
    # def run_scheduled_job():
    #     try:
    #         asyncio.run(run_daily_fg_job_and_import())
    #     except Exception as e:
    #         logging.critical(f"调度任务运行时出错: {e}", exc_info=True)
    #
    # schedule.every().day.at("09:00").do(run_scheduled_job)
    # logging.info("任务已安排在每天 09:00 运行。调度器启动...")
    # while True:
    #     schedule.run_pending()
    #     time.sleep(60)

    logging.info("脚本执行完毕。")