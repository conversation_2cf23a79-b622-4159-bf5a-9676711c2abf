# Fear & Greed Index 数据导入脚本 (`fear_greed_async_importer.py`)

## 1. 目标

本脚本旨在自动、健壮地从 CNN 的 API 获取每日恐惧与贪婪指数 (Fear & Greed Index) 数据，并将其增量、批量地导入（Upsert）到 PostgreSQL 数据库 (`ts_research.public.fear_greed_index_daily`) 中。

## 2. 开发过程与问题解决

### 2.1 初始方案：全异步

最初的计划是采用全异步模型：
*   使用 `aiohttp` 库进行异步网络请求获取 API 数据。
*   使用 `asyncpg` 库进行异步数据库连接和批量 Upsert 操作。

### 2.2 遇到的问题：HTTP 418 错误

在实施过程中，使用 `aiohttp` 的脚本在尝试访问 CNN API (`https://production.dataviz.cnn.io/index/fearandgreed/graphdata`) 时，持续收到 `HTTP 418 Unknown Error`（"I'm a teapot"）错误。

### 2.3 故障排查

*   **对比成功脚本：** 发现一个简单的同步脚本 (`get_fear_greed_index.py`) 使用 `requests` 库可以成功获取数据。
*   **简化 Headers：** 尝试简化 `aiohttp` 请求中的 HTTP Headers，使其接近成功脚本（仅保留 `User-Agent`），但问题依旧存在。这表明服务器可能不仅仅基于 Headers 来识别和拒绝 `aiohttp` 的请求，可能还涉及更复杂的请求模式或 TLS 指纹等检测。
*   **切换方案：** 由于无法通过调整 `aiohttp` 参数稳定解决 418 错误，决定采纳成功脚本的经验。

### 2.4 最终解决方案：混合模型

最终实现采用了混合模型：
*   **网络请求:** 改用 **同步** 的 `requests` 库来获取 API 数据。这被证明是稳定和可靠的。
*   **数据库操作:** 保持使用 **异步** 的 `asyncpg` 库，以利用其在数据库交互方面的高性能和效率。

## 3. 最终架构与逻辑

脚本的核心逻辑流程如下：

1.  **加载配置 (`config.ini`)**: 读取数据库连接信息、API URL、日志设置等。
2.  **设置日志**: 配置日志记录器，同时输出到文件 (`fear_greed_importer.log`) 和控制台。
3.  **创建数据库连接池 (`asyncpg`)**: 异步建立与 PostgreSQL 的连接池。
4.  **确保表结构存在**: 异步检查并按需创建目标数据表 (`public.fear_greed_index_daily`) 及其索引。
5.  **获取 API 数据 (同步 `requests`)**: 使用 `requests.get` 同步调用 CNN API 获取 JSON 数据。包含基本的错误处理。
6.  **解析数据**: 解析返回的 JSON，提取日期 (`date`)、指标值 (`fg_value`) 和评级 (`rating`)。
7.  **获取数据库最新日期**: 异步查询数据库中已存在的最新数据日期。
8.  **过滤新数据**: 对比 API 数据和数据库最新日期，筛选出需要插入或更新的新记录。
9.  **批量导入/更新 (异步 `asyncpg`)**: 将新数据整理成元组列表，使用 `asyncpg` 的 `executemany` 和 `INSERT ... ON CONFLICT DO UPDATE` 语句进行高效的批量 Upsert。包含批处理和事务管理。
10. **清理资源**: 关闭 `asyncpg` 连接池。
11. **记录总结**: 记录任务耗时、成功/失败状态以及导入/更新的记录数。

## 4. 依赖项

*   `requests`: 用于同步 HTTP 请求。
*   `asyncpg`: 用于异步 PostgreSQL 数据库交互。
*   `pandas`: 用于数据解析和处理（尤其是在 `parse_fear_greed_response` 中）。
*   `configparser`: 用于读取 `.ini` 配置文件。

请确保通过 `pip install requests asyncpg pandas` 安装这些库。

## 5. 配置 (`config.ini`)

脚本需要一个 `config.ini` 文件来配置数据库连接和 API 参数。示例：

```ini
[database]
dbname = ts_research
user = your_db_user
password = your_db_password
host = localhost
port = 5432

[api]
url = https://production.dataviz.cnn.io/index/fearandgreed/graphdata
# headers 在代码中设置，通常无需在此配置
timeout = 30

[logging]
log_file = ./fear_greed_importer.log
log_level = INFO

[import]
batch_size = 500
```

**注意:** 请将数据库凭据替换为实际值，并确保生产环境中不硬编码敏感信息。

## 6. 如何运行

直接在命令行中运行脚本：

```bash
python fear_greed_async_importer.py
```

脚本将执行一次数据获取和导入操作，并将日志输出到控制台和 `fear_greed_importer.log` 文件。

## 7. 可能遇到的问题

*   **API 更改**: CNN API 的 URL、响应格式或访问限制可能发生变化，导致脚本失败。
*   **网络问题**: 无法连接到 API 服务器或数据库服务器。
*   **数据库问题**: 连接凭据错误、数据库服务未运行、磁盘空间不足等。
*   **依赖库更新**: `requests` 或 `asyncpg` 的未来版本可能引入不兼容的更改。